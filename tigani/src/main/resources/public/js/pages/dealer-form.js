var oldImageBase64;

const DealerForm = function () {
    // Initialization of components
    const init = function () {
        _componentFilePond();
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitDealer();
    };


    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Input with icons and Select2
                if (element.hasClass('ckeditor')) {
                    error.appendTo(element.parent());
                }

                // Input with icons and Select2
                else if (element.hasClass('select')) {
                    error.appendTo(element.parent());
                }

                // Input group, form checks and custom controls
                else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            }
        });

        // Custom validation methods for dealer form
        $.validator.addMethod('vatNumber', function (value, element) {
            return this.optional(element) || /^[0-9]{11}$/.test(value);
        }, 'Partita IVA deve contenere esattamente 11 cifre.');

        $.validator.addMethod('fiscalCode', function (value, element) {
            return this.optional(element) || /^[A-Z0-9]{16}$/.test(value.toUpperCase());
        }, 'Codice fiscale deve contenere esattamente 16 caratteri alfanumerici.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    const _componentDeleteButton = function () {
        const deleteBtn = document.getElementById('delete-dealer-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                // Check permission first
                if (!hasPermission('DEALER_MANAGEMENT', 'delete')) {
                    showToast('Non hai i permessi per eseguire questa operazione.', 'error');
                    return;
                }

                const dealerId = $(this).data('dealerid');
                if (!dealerId) {
                    showToast('Errore: ID dealer non trovato', 'error');
                    return;
                }
                // Show confirmation dialog
                $.confirm({
                    title: 'Conferma eliminazione',
                    content: 'Sei sicuro di voler eliminare questo rivenditore? Questa azione non può essere annullata.',
                    type: 'red',
                    typeAnimated: true,
                    buttons: {
                        elimina: {
                            text: 'Elimina',
                            btnClass: 'btn-red',
                            action: function () {
                                if (dealerId) {
                                    // Call delete operation
                                    const formData = new FormData();
                                    formData.append('dealerIds', dealerId);
                                    formData.append('operation', 'delete');

                                    $.blockUI();
                                    $.ajax({
                                        url: appRoutes.get('BE_DEALER_OPERATE'),
                                        type: 'POST',
                                        data: formData,
                                        processData: false,
                                        contentType: false,
                                        success: function(response) {
                                            const $form = $(this);

                                            // Check if we're in an offcanvas (for future compatibility)
                                            const isOffcanvas = $form.closest('.hs-overlay').length > 0;

                                            // Show success toast and redirect after delay
                                            showToast('Rivenditore salvato correttamente', 'success');
                                            if (isOffcanvas) {
                                                $.unblockUI();
                                            } else {
                                                setTimeout(() => {
                                                    window.location.href = appRoutes.get('BE_DEALER_COLLECTION');
                                                }, 1000);
                                            }
                                        },
                                        error: function(xhr, status, error) {
                                            $.unblockUI();
                                            let errorMessage = 'Errore durante l\'eliminazione';
                                            if (xhr.responseText) {
                                                errorMessage = xhr.responseText;
                                            }
                                            showToast(errorMessage, 'error');
                                            console.error('Error during dealer deletion:', error);
                                        }
                                    });
                                }
                            }
                        },
                        annulla: {
                            text: 'Annulla',
                            btnClass: 'btn-light'
                        }
                    }
                });
            });
        }
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('DEALER_MANAGEMENT', 'edit')) {
            // Disable all form inputs for both page and offcanvas forms
            $('#dealer-edit input, #dealer-edit textarea, #dealer-edit select').prop('readonly', true);
            $('#dealer-edit select').prop('disabled', true);
            $('#dealer-edit-offcanvas input, #dealer-edit-offcanvas textarea, #dealer-edit-offcanvas select').prop('readonly', true);
            $('#dealer-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#dealer-edit, #dealer-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new dealer forms
        const isNewDealerPage = !$('#dealer-edit input[name="id"]').val();
        const isNewDealerOffcanvas = !$('#dealer-edit-offcanvas input[name="id"]').val();
        const isNewDealer = isNewDealerPage || isNewDealerOffcanvas;

        if (isNewDealer && !hasPermission('DEALER_MANAGEMENT', 'create')) {
            // Disable all form inputs for both page and offcanvas forms
            $('#dealer-edit input, #dealer-edit textarea, #dealer-edit select').prop('disabled', true);
            $('#dealer-edit-offcanvas input, #dealer-edit-offcanvas textarea, #dealer-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#dealer-edit, #dealer-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitDealer = function () {
        // Form submission handling - works for both offcanvas and page forms
        $('#dealer-edit-offcanvas, #dealer-edit').submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewDealer = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewDealer) {
                    if (!hasPermission('DEALER_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare rivenditori.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('DEALER_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare rivenditori.', 'error');
                        return;
                    }
                }

                // Handle FilePond file if present
                try {
                    if (pond.getFiles().length > 0) {
                        // Ottieni la stringa base64 del file croppato
                        const fileToInsert = pond.getFiles()[0];
                        const base64String = fileToInsert.getFileEncodeBase64String();

                        if (oldImageBase64 === base64String) {
                            // add field to specify that image is the same and should not be updated
                            formData.append('sameImage', true);
                        } else {
                            const mimeType = fileToInsert.fileType;
                            const blob = base64ToBlob(base64String, mimeType);
                            const fileName = fileToInsert.filename;
                            const file = new File([blob], fileName, {type: mimeType});

                            // Aggiungi il file croppato al FormData
                            formData.append('file', file);
                        }
                    }
                } catch (fileError) {
                    console.warn('Error processing file upload:', fileError);
                    // Continue without file if there's an error
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        const $form = $(this);

                        try {
                            // Check if we're in an offcanvas (for future compatibility)
                            const isOffcanvas = $form.closest('.hs-overlay').length > 0;

                            if (isOffcanvas) {
                                $.unblockUI();
                                // Close offcanvas and reload table
                                const overlayElement = $form.closest(".hs-overlay.opened")[0];
                                if (overlayElement) {
                                    const overlay = HSOverlay.getInstance(overlayElement, true);
                                    if (overlay) {
                                        overlay.element.close();
                                    }
                                }

                                // Reload table with error handling
                                if (window.dealersDataTable && window.dealersDataTable.dataTable) {
                                    window.dealersDataTable.dataTable.ajax.reload(null, false); // Keep current page
                                }

                                // Show success message
                                showToast('Rivenditore salvato correttamente', 'success');
                            } else {
                                showToast('Rivenditore salvato correttamente', 'success');
                                setTimeout(() => {
                                    window.location.href = appRoutes.get('BE_DEALER_COLLECTION');
                                }, 1000);
                            }
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            // Show error toast and redirect after delay
                            showToast('Si è verificato un errore', 'error');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during dealer save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // FilePond
    const _componentFilePond = function () {

        // Social share preview

        FilePond.registerPlugin(
                FilePondPluginImageExifOrientation,
                FilePondPluginImagePreview,
                FilePondPluginImageCrop,
                FilePondPluginImageResize,
                FilePondPluginImageFilter,
                FilePondPluginImageTransform,
                FilePondPluginImageEdit,
                FilePondPluginImageValidateSize,
                FilePondPluginFileEncode,
                FilePondPluginFileValidateType,
                FilePondPluginFileValidateSize
                );
        FilePond.setOptions(FilePondIT);

        // Support both regular form and offcanvas form
        const inputElement = document.querySelector('#dealer-edit input[type="file"]') ||
                             document.querySelector('#dealer-edit-offcanvas input[type="file"]') ||
                             document.querySelector('#logo-offcanvas');

        const doka = dokaCreate({
            //utils: 'crop, color',
            cropAspectRatioOptions: [
                {
                    label: 'Logo',
                    value: 1 / 1
                }
            ],
            crop: {
                aspectRatio: 1 / 1
            },
            labelButtonReset: "Reimposta",
            labelButtonCancel: "Annulla",
            labelButtonConfirm: "Conferma",
            labelButtonUtilCrop: "Ritaglia",
            labelButtonUtilResize: "Ridimensiona",
            labelButtonUtilFilter: "Filtra",
            labelButtonUtilColor: "Colori",
            labelButtonUtilMarkup: "Annota",
            labelStatusMissingWebGL: "WebGL è richiesto ma è disabilitato nel tuo browser",
            labelStatusAwaitingImage: "In attesa dell'immagine…",
            labelStatusLoadImageError: "Errore nel caricamento dell'immagine…",
            labelStatusLoadingImage: "Caricamento dell'immagine…",
            labelStatusProcessingImage: "Elaborazione dell'immagine…",
            labelColorBrightness: "Luminosità",
            labelColorContrast: "Contrasto",
            labelColorExposure: "Esposizione",
            labelColorSaturation: "Saturazione",
            labelMarkupTypeRectangle: "Rettangolo",
            labelMarkupTypeEllipse: "Cerchio",
            labelMarkupTypeText: "Testo",
            labelMarkupTypeLine: "Linea",
            labelMarkupSelectFontSize: "Dimensione",
            labelMarkupSelectFontFamily: "Carattere",
            labelMarkupSelectLineDecoration: "Decorazione",
            labelMarkupSelectLineStyle: "Stile",
            labelMarkupSelectShapeStyle: "Stile",
            labelMarkupRemoveShape: "Rimuovi",
            labelMarkupToolSelect: "Seleziona",
            labelMarkupToolDraw: "Disegna",
            labelMarkupToolLine: "Linea",
            labelMarkupToolText: "Testo",
            labelMarkupToolRect: "Rettangolo",
            labelMarkupToolEllipse: "Cerchio",
            labelResizeWidth: "Larghezza",
            labelResizeHeight: "Altezza",
            labelResizeApplyChanges: "Applica modifiche",
            labelCropInstructionZoom: "Zoom avanti e indietro con la rotellina del mouse o il touchpad.",
            labelButtonCropZoom: "Zoom",
            labelButtonCropRotateLeft: "Ruota a sinistra",
            labelButtonCropRotateRight: "Ruota a destra",
            labelButtonCropRotateCenter: "Centra rotazione",
            labelButtonCropFlipHorizontal: "Rifletti orizzontalmente",
            labelButtonCropFlipVertical: "Rifletti verticalmente",
            labelButtonCropAspectRatio: "Proporzioni",
            labelButtonCropToggleLimit: "Selezione ritaglio",
            labelButtonCropToggleLimitEnable: "Limitato all'immagine",
            labelButtonCropToggleLimitDisable: "Seleziona fuori immagine",
            pointerEventsPolyfillScope: "ambito",
            styleCropCorner: "angolo",
            styleFullscreenSafeArea: "area sicura a schermo intero"

        })

        pond = FilePond.create(inputElement, {
            allowFileEncode: true,
            allowImagePreview: true,
            allowImageTransform: true,
            allowImageEdit: true,
            allowImageCrop: true,
            stylePanelLayout: 'compact circle',
            stylePanelAspectRatio: '1:1',
            styleLoadIndicatorPosition: 'center bottom',
            styleProgressIndicatorPosition: 'right bottom',
            styleButtonRemoveItemPosition: 'left bottom',
            styleButtonProcessItemPosition: 'right bottom',
            imageCropAspectRatio: '1:1',
            imagePreviewHeight: 200,
            imageResizeTargetWidth: 400,
            imageResizeTargetHeight: 400,
            allowImageResize: true,
            allowImageExifOrientation: true,
            maxFileSize: '3MB',
            acceptedFileTypes: ['image/png', 'image/jpeg', 'image/svg'],
            imageEditEditor: doka
        });

        // Load initial image if present
        var imageId = pageVariables.get("imageId");
        if (typeof imageId !== "undefined" && imageId) {
            var image = appRoutes.get("BE_IMAGE") + "?oid=" + pageVariables.get("imageId").replace("[", "").replace("]", "");
            pond.addFile(image).then(file => {
                console.log('Image loaded:', file);
                oldImageBase64 = file.getFileEncodeBase64String();
            }).catch(err => {
                console.error('Error loading image', err);
            });
        }
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Funzione ausiliaria per convertire base64 in Blob
function base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
    }

    const byteArray = new Uint8Array(byteArrays);
    return new Blob([byteArray], {type: mimeType});
}
