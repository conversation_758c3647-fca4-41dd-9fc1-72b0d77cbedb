const ContactForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitContact();
        _componentContactTypeHandler();
    };

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Input with icons and Select2
                if (element.hasClass('ckeditor')) {
                    error.appendTo(element.parent());
                }

                // Input with icons and Select2
                else if (element.hasClass('select')) {
                    error.appendTo(element.parent());
                }

                // Input group, form checks and custom controls
                else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            }
        });

        // Custom email validation
        $.validator.addMethod('email', function (value, element) {
            return this.optional(element) || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        }, 'Inserisci un indirizzo email valido.');

        // Custom VAT number validation
        $.validator.addMethod('vatNumber', function (value, element) {
            return this.optional(element) || /^[0-9]{11}$/.test(value.replace(/\s/g, ''));
        }, 'Inserisci una partita IVA valida (11 cifre).');

        // Custom phone validation
        $.validator.addMethod('phone', function (value, element) {
            return this.optional(element) || /^[\+]?[0-9\s\-\(\)]{8,20}$/.test(value);
        }, 'Inserisci un numero di telefono valido.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    const _componentDeleteButton = function () {
        const deleteBtn = document.getElementById('delete-contact-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                // Check permission first
                if (!hasPermission('CONTACT_MANAGEMENT', 'delete')) {
                    showToast('Non hai i permessi per eseguire questa operazione.', 'error');
                    return;
                }

                const contactId = $(this).data('contactid');
                if (!contactId) {
                    showToast('Errore: ID contatto non trovato', 'error');
                    return;
                }
                // Show confirmation dialog
                $.confirm({
                    title: 'Conferma eliminazione',
                    content: 'Sei sicuro di voler eliminare questo contatto? Questa azione non può essere annullata.',
                    type: 'red',
                    typeAnimated: true,
                    buttons: {
                        elimina: {
                            text: 'Elimina',
                            btnClass: 'btn-red',
                            action: function () {
                                if (contactId) {
                                    // Call delete operation
                                    const formData = new FormData();
                                    formData.append('contactIds', contactId);
                                    formData.append('operation', 'delete');

                                    $.blockUI();
                                    $.ajax({
                                        url: appRoutes.get('BE_CONTACT_OPERATE'),
                                        type: 'POST',
                                        data: formData,
                                        processData: false,
                                        contentType: false,
                                        success: function(response) {
                                            $.unblockUI();

                                            // Close offcanvas
                                            const offcanvasElement = deleteBtn.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                                            if (offcanvasElement) {
                                                const overlay = HSOverlay.getInstance(offcanvasElement, true);
                                                if (overlay) {
                                                    overlay.element.close();
                                                }
                                            }

                                            // Reload table
                                            if (window.contactsDataTable && window.contactsDataTable.dataTable) {
                                                window.contactsDataTable.dataTable.ajax.reload();
                                            }

                                            // Show success message
                                            showToast('Contatto eliminato correttamente', 'success');
                                        },
                                        error: function(xhr, status, error) {
                                            $.unblockUI();
                                            let errorMessage = 'Errore durante l\'eliminazione';
                                            if (xhr.responseText) {
                                                errorMessage = xhr.responseText;
                                            }
                                            showToast(errorMessage, 'error');
                                            console.error('Error during contact deletion:', error);
                                        }
                                    });
                                }
                            }
                        },
                        annulla: {
                            text: 'Annulla',
                            btnClass: 'btn-light'
                        }
                    }
                });
            });
        }
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('CONTACT_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#contact-edit input, #contact-edit textarea, #contact-edit select').prop('readonly', true);
            $('#contact-edit select').prop('disabled', true);
            $('#contact-edit-offcanvas input, #contact-edit-offcanvas textarea, #contact-edit-offcanvas select').prop('readonly', true);
            $('#contact-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#contact-edit, #contact-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new contact forms
        const isNewContact = !$('#contact-edit input[name="id"]').val() && !$('#contact-edit-offcanvas input[name="id"]').val();
        if (isNewContact && !hasPermission('CONTACT_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#contact-edit input, #contact-edit textarea, #contact-edit select').prop('disabled', true);
            $('#contact-edit-offcanvas input, #contact-edit-offcanvas textarea, #contact-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#contact-edit, #contact-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitContact = function () {
        var idForm = "contact-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewContact = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewContact) {
                    if (!hasPermission('CONTACT_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare contatti.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('CONTACT_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare contatti.', 'error');
                        return;
                    }
                }

                // Handle userIds array - get selected values from the advanced select
                const userIdsSelect = document.getElementById('userIds');
                if (userIdsSelect) {
                    const selectedUserIds = Array.from(userIdsSelect.selectedOptions).map(option => option.value);

                    // Remove existing userIds entries and add the array
                    formData.delete('userIds');
                    selectedUserIds.forEach(userId => {
                        if (userId) { // Only add non-empty values
                            formData.append('userIds', userId);
                        }
                    });
                }

                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.contactsDataTable && window.contactsDataTable.dataTable) {
                                window.contactsDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Contatto salvato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Contatto salvato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during contact save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Contact type change handler
    const _componentContactTypeHandler = function () {
        const contactTypeSelect = document.getElementById('contactType');
        if (contactTypeSelect) {
            contactTypeSelect.addEventListener('change', _handleContactTypeChange);
            // Initialize on page load if a type is already selected
            if (contactTypeSelect.value) {
                _handleContactTypeChange.call(contactTypeSelect);
            }
        }
    };

    // Handle contact type change to show/hide relevant fields
    function _handleContactTypeChange() {
        const contactType = this.value;
        const personFields = document.getElementById('person-fields');
        const companyFields = document.getElementById('company-fields');

        if (contactType === 'PERSON') {
            personFields.classList.remove('hidden');
            companyFields.classList.add('hidden');

            // Update validation rules
            $('#firstName').rules('add', {
                required: true,
                messages: {
                    required: 'Il nome è obbligatorio per le persone.'
                }
            });
            $('#lastName').rules('add', {
                required: true,
                messages: {
                    required: 'Il cognome è obbligatorio per le persone.'
                }
            });
            $('#companyName').rules('remove', 'required');
        } else if (contactType === 'COMPANY') {
            personFields.classList.add('hidden');
            companyFields.classList.remove('hidden');

            // Update validation rules
            $('#companyName').rules('add', {
                required: true,
                messages: {
                    required: 'La ragione sociale è obbligatoria per le aziende.'
                }
            });
            $('#firstName').rules('remove', 'required');
            $('#lastName').rules('remove', 'required');
        } else {
            // No type selected - hide both
            personFields.classList.add('');
            companyFields.classList.add('hidden');

            // Remove all conditional validation rules
            $('#firstName').rules('remove', 'required');
            $('#lastName').rules('remove', 'required');
            $('#companyName').rules('remove', 'required');
        }
    }

    // Return objects assigned to module
    return {
        init: init
    };
}();
