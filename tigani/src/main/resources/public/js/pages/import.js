$(function () {

    // Initialize form validation
    const _componentValidation = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize form validation
        $('.form-validate').validate({
            ignore: 'input[type=hidden], .select2-search__field',
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function(element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function(element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },
            errorPlacement: function(error, element) {
                if (element.hasClass('select2-hidden-accessible')) {
                    error.appendTo(element.parent());
                } else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            }
        });
    };

    // Initialize file uploader
    const _componentFileUpload = function () {
        // File upload configuration
        $('input[type="file"]').each(function() {
            const $input = $(this);
            const maxSize = $input.data('maxfilessize') || 10485760; // 10MB default
            
            $input.on('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file size
                    if (file.size > maxSize) {
                        new Noty({
                            text: 'Il file è troppo grande. Dimensione massima: ' + (maxSize / 1024 / 1024) + 'MB',
                            type: 'error'
                        }).show();
                        $(this).val('');
                        return;
                    }
                    
                    // Validate file type
                    const allowedTypes = [
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'application/vnd.ms-excel'
                    ];
                    
                    if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls')) {
                        new Noty({
                            text: 'Formato file non valido. Sono accettati solo file Excel (.xlsx, .xls)',
                            type: 'error'
                        }).show();
                        $(this).val('');
                        return;
                    }
                }
            });
        });
    };

    // Handle import type selection
    const _handleImportTypeChange = function () {
        $('select[name="importType"]').on('change', function() {
            const selectedType = $(this).val();
            const $fileInput = $('input[name="excelFile"]');
            const $fileContainer = $fileInput.closest('.row');

            // Hide all info sections
            $('#comuni-info, #stati-info, #province-info, #marche-info, #modelli-info, #allestimenti-info').hide();

            // Show relevant info section and handle file requirement
            if (selectedType === 'comuni') {
                $('#comuni-info').show();
                $fileContainer.show();
                $fileInput.prop('required', true);
            } else if (selectedType === 'stati') {
                $('#stati-info').show();
                $fileContainer.show();
                $fileInput.prop('required', true);
            } else if (selectedType === 'province') {
                $('#province-info').show();
                $fileContainer.show();
                $fileInput.prop('required', true);
            } else if (selectedType === 'marche') {
                $('#marche-info').show();
                $fileContainer.hide();
                $fileInput.prop('required', false);
            } else if (selectedType === 'modelli') {
                $('#modelli-info').show();
                $fileContainer.hide();
                $fileInput.prop('required', false);
            } else if (selectedType === 'allestimenti') {
                $('#allestimenti-info').show();
                $fileContainer.hide();
                $fileInput.prop('required', false);
            } else {
                // Default case - show file input
                $fileContainer.show();
                $fileInput.prop('required', true);
            }
        });
    };

    // Handle form submission
    const _handleFormSubmission = function () {
        $('#import-form').on('submit', function(e) {
            e.preventDefault();
            
            if (!$(this).valid()) {
                return;
            }
            
            const formData = new FormData(this);
            const $submitBtn = $('#import-btn');
            const $resetBtn = $('#reset-btn');
            const $progressContainer = $('#import-progress');
            const $progressBar = $progressContainer.find('.progress-bar');
            const $progressText = $('#progress-text');
            const $resultsContainer = $('#import-results');
            const $resultsContent = $('#results-content');
            
            // Disable form and show progress
            $submitBtn.prop('disabled', true).html('<i class="ph-spinner ph-spin me-2"></i>Importazione in corso...');
            $progressContainer.show();
            $resultsContainer.hide();
            $progressBar.css('width', '10%');
            
            // Simulate progress updates
            let progress = 10;
            const progressInterval = setInterval(function() {
                if (progress < 90) {
                    progress += Math.random() * 20;
                    if (progress > 90) progress = 90;
                    $progressBar.css('width', progress + '%');
                }
            }, 500);
            
            // Submit form
            $.ajax({
                url: appRoutes.get('BE_IMPORT_PROCESS'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 600000, // 10 minutes timeout
                success: function(response) {
                    clearInterval(progressInterval);
                    $progressBar.css('width', '100%');
                    $progressText.text('Importazione completata!');
                    
                    setTimeout(function() {
                        $progressContainer.hide();
                        
                        let alertClass = 'alert-success';
                        let iconClass = 'ph-check-circle';
                        let message = response.message;
                        
                        if (response.imported !== undefined) {
                            message += ' (' + response.imported + ' record importati)';
                        }
                        
                        $resultsContent.html(
                            '<div class="alert ' + alertClass + '">' +
                            '<i class="' + iconClass + ' me-2"></i>' +
                            '<strong>Successo!</strong> ' + message +
                            '</div>'
                        );
                        
                        $resultsContainer.show();
                        $resetBtn.show();
                        
                        new Noty({
                            text: 'Importazione completata con successo!',
                            type: 'success'
                        }).show();
                        
                    }, 1000);
                },
                error: function(xhr) {
                    clearInterval(progressInterval);
                    $progressContainer.hide();
                    
                    let errorMessage = 'Errore durante l\'importazione';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        // Use default error message
                    }
                    
                    $resultsContent.html(
                        '<div class="alert alert-danger">' +
                        '<i class="ph-x-circle me-2"></i>' +
                        '<strong>Errore!</strong> ' + errorMessage +
                        '</div>'
                    );
                    
                    $resultsContainer.show();
                    $resetBtn.show();
                    
                    new Noty({
                        text: errorMessage,
                        type: 'error'
                    }).show();
                },
                complete: function() {
                    $submitBtn.prop('disabled', false).html('<i class="ph-upload me-2"></i>Avvia Importazione');
                }
            });
        });
    };

    // Handle reset button
    const _handleReset = function () {
        $('#reset-btn').on('click', function() {
            // Reset form
            $('#import-form')[0].reset();
            
            // Hide all dynamic sections
            $('#comuni-info, #stati-info, #province-info, #marche-info, #modelli-info, #allestimenti-info, #import-progress, #import-results').hide();

            // Show file input container and make it required by default
            $('input[name="excelFile"]').closest('.row').show();
            $('input[name="excelFile"]').prop('required', true);
            
            // Reset buttons
            $('#import-btn').show();
            $(this).hide();
            
            // Clear validation
            $('#import-form').validate().resetForm();
            $('#import-form .is-valid, #import-form .is-invalid').removeClass('is-valid is-invalid');
        });
    };

    // Initialize all components
    _componentValidation();
    _componentFileUpload();
    _handleImportTypeChange();
    _handleFormSubmission();
    _handleReset();

});
