<!-- Dealer Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_DEALER_VIEW', '{{ routes("BE_DEALER_VIEW") }}');
    addRoute('BE_DEALER_SAVE', '{{ routes("BE_DEALER_SAVE") }}');
    addRoute('BE_DEALER_OPERATE', '{{ routes("BE_DEALER_OPERATE") }}');
    addRoute('BE_DEALER_COLLECTION', '{{ routes("BE_DEALER_COLLECTION") }}');
    addVariables('imageId', '{{ curDealer.imageId }}');
</script>

<!-- Card -->
<div class="bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    {% set postUrl = routes('BE_DEALER_SAVE') %}
    {% if curDealer.id is not empty %}
    {% set postUrl = routes('BE_DEALER_SAVE') + '?dealerId=' + curDealer.id %}
    {% endif %}

    <form id="{% if formId is defined %}{{ formId }}{% else %}dealer-edit-offcanvas{% endif %}" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery">
        {% if curDealer.id is not empty %}
        <input type="hidden" name="id" value="{{ curDealer.id }}">
        {% endif %}
        <div class="py-2 sm:py-4 px-2">
            <div class="p-2 sm:p-4 space-y-3">
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Immagine profilo
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="logo-offcanvas" name="imageId" type="file" class="filepond w-[100px] cursor-pointer">
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="fullname" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Ragione Sociale <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="fullname" name="fullname" placeholder="Ragione sociale" value="{{ curDealer.fullname }}" type="text" required maxlength="100" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="dealerCode" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Codice Rivenditore <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="dealerCode" name="dealerCode" type="text" maxlength="100" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Codice Rivenditore" value="{{ curDealer.dealerCode }}" required {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="vatNumber" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Partita IVA <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="vatNumber" name="vatNumber" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Partita IVA" value="{{ curDealer.vatNumber }}" required maxlength="11" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="fiscalCode" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Codice fiscale
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="fiscalCode" name="fiscalCode" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Codice fiscale" value="{{ curDealer.fiscalCode }}" maxlength="16" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="sdi" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Codice SDI
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="sdi" name="sdi" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Codice SDI" value="{{ curDealer.sdi }}" maxlength="7" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="pec" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            PEC <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">                        
                        <input id="pec" name="pec" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="PEC" value="{{ curDealer.pec }}" required maxlength="100" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="email" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Email <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">                        
                        <input id="email" name="email" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Email" value="{{ curDealer.email }}" required {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="phoneNumber" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Telefono <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <div class="space-y-3">
                            <div class="grid grid-cols-12 gap-2">
                                <div class="col-span-4">
                                    <div class="relative">
                                        <select readonly data-hs-select='{
                                                "hasSearch": true,
                                                "placeholder": "Stato",
                                                "searchPlaceholder": "Cerca stato...",
                                                "searchNoResultText": "Nessun risultato",
                                                "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400 py-1.5 sm:py-2 px-3",
                                                "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",
                                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                                                "dropdownClasses": "max-h-64 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900",
                                                "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                                "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>",
                                                "preventSearchFocus": false,
                                                "viewport": "#hs-pro-create-new-user"
                                                }' class="hidden">
                                            <option value="">Seleziona stato</option>

                                            <option value="+39" selected data-hs-select-option='{
                                                    "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/lang/it.svg\" alt=\"Italy\" />"}'>
                                                + 39
                                            </option>                        
                                        </select>

                                        <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                            <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-span-8">
                                    <input id="phoneNumber" name="phoneNumber" type="text" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Telefono" value="{{ curDealer.phoneNumber }}" required {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="website" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Sito web
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">                                                                        
                        <div class="relative">
                            <input type="text" id="website" name="website" class="py-1.5 sm:py-2 px-3 ps-16 block w-full border-gray-200 rounded-lg sm:text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="www.miosito.com" value="{{ curDealer.website }}" maxlength="100" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                                <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-4">
                                    <span class="text-sm text-gray-500 dark:text-neutral-500">https://</span>
                                </div>
                        </div>                                                    
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->                

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="fullAddress" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Indirizzo <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->
                    
                    <div class="sm:col-span-9 space-y-3">
                        
                        <!-- Full address -->
                        <div>
                            <input id="fullAddress" name="fullAddress" type="text" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Indirizzo completo" value="{{ curDealer.fullAddress }}" maxlength="200" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                            <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">Scrivi l'indirizzo completo i campi sottostanti si autocompileranno</p>
                        </div>                        
                        <!-- End Full address -->
                        
                        <hr class="border-gray-200">
                                                
                        <!-- Address + Street Number -->
                        <div class="space-y-3">                            
                            <div class="grid grid-cols-12 gap-2">
                                <div class="col-span-9">
                                    <input id="address" name="address" type="text" placeholder="Via\Piazza" value="{{ curDealer.address }}" required maxlength="100" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                                </div>
                                <div class="col-span-3">
                                    <input id="streetNumber" name="streetNumber" type="text" placeholder="Civico" value="{{ curDealer.streetNumber }}" required maxlength="5" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                                </div>
                            </div>
                        </div>
                        <!-- End Address + Street Number -->
                            
                        <!-- City -->
                        <div class="space-y-3">                                
                            <div class="relative">
                                <select id="city" name="city" data-hs-select='{
                                        "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_CITIES") }}",
                                        "apiQuery": "selected={{ curDealer.city }}&q",
                                        "apiSearchMinLength": 1,
                                        "apiDataPart": "results",
                                        "apiFieldsMap": {
                                        "id": "id",
                                        "val": "id",
                                        "title": "text"
                                        },
                                        "apiSelectedValues": "{{ curDealer.city }}",
                                        "hasSearch": true,
                                        "apiLoadMore": true,
                                        "placeholder": "Cerca città...",
                                        "toggleTag": "<button type=\"button\"></button>",
                                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                                        "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                        "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                                        }' {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}disabled{% endif %}>                          
                                </select>
                                <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                    <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <!-- End City -->
                        
                        <!-- Postal Code + Province Code -->
                        <div class="grid grid-cols-2 gap-x-3">                                
                            <div class="relative">                          
                                <select id="postalCode" name="postalCode" data-hs-select='{
                                        "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_PROVINCES") }}",
                                        "apiQuery": "selected={{ curDealer.postalCode }}&q",
                                        "apiSearchMinLength": 1,
                                        "apiDataPart": "results",
                                        "apiFieldsMap": {
                                        "id": "id",
                                        "val": "id",
                                        "title": "text"
                                        },
                                        "apiSelectedValues": "{{ curDealer.postalCode }}",
                                        "hasSearch": true,
                                        "apiLoadMore": true,
                                        "placeholder": "CAP",
                                        "placeholder": "Cerca cap...",
                                        "toggleTag": "<button type=\"button\"></button>",
                                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                                        "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                        "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                                        }' {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}disabled{% endif %}>
                                </select>
                                <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                    <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </div>
                            </div>
                            <div class="relative">                          
                                <select id="provinceCode" name="provinceCode" data-hs-select='{
                                        "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_PROVINCES") }}",
                                        "apiQuery": "selected={{ curDealer.provinceCode }}&q",
                                        "apiSearchMinLength": 1,
                                        "apiDataPart": "results",
                                        "apiFieldsMap": {
                                        "id": "id",
                                        "val": "id",
                                        "title": "text"
                                        },
                                        "apiSelectedValues": "{{ curDealer.provinceCode }}",
                                        "hasSearch": true,
                                        "apiLoadMore": true,
                                        "placeholder": "Cerca provincia...",
                                        "toggleTag": "<button type=\"button\"></button>",
                                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                                        "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                        "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                                        }' {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}disabled{% endif %}>
                                </select>
                                <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                    <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <!-- End Postal Code + Province Code -->
                        
                        <!-- Country -->
                        <div class="relative">
                            <select id="countryCode" name="countryCode" readonly data-hs-select='{
                                    "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_COUNTRIES") }}",
                                    "apiQuery": "selected={{ curDealer.countryCode }}&q",
                                    "apiSearchMinLength": 1,
                                    "apiDataPart": "results",
                                    "apiFieldsMap": {
                                        "id": "id",
                                        "val": "id",
                                        "title": "text"
                                    },
                                    "apiSelectedValues": "{{ curDealer.countryCode }}",
                                    "hasSearch": true,
                                    "apiLoadMore": true,
                                    "placeholder": "Cerca paese...",
                                    "toggleTag": "<button type=\"button\"></button>",
                                    "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                                    "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                                    "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                    "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                                    }' class="hidden">
                                <option value="">Seleziona stato</option>

                                <!--<option value="IT" selected data-hs-select-option='{
                                        "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/lang/it.svg\" alt=\"Italy\" />"}'>
                                    Italia
                                </option>-->
                            </select>

                            <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m7 15 5 5 5-5" />
                                    <path d="m7 9 5-5 5 5" />
                                </svg>
                            </div>
                        </div>
                        <!-- End Country -->
                        
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
            </div>
        </div>

        <!-- Footer -->
        <div class="p-6 pt-0 flex justify-between gap-x-2">
            <div class="flex items-center gap-x-2">
                {% if curDealer.id is not empty and user.hasPermission('DEALER_MANAGEMENT', 'delete') %}
                <button type="button" id="delete-dealer-btn" data-dealerid="{{ curDealer.id }}" class="py-2 px-3 inline-flex justify-center items-center gap-x-2 text-start bg-red-600 border border-red-600 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-red-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-1 focus:ring-red-300 dark:focus:ring-red-500">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                    Elimina
                </button>
                {% endif %}
            </div>
            <div class="flex items-center gap-x-2">
                <a href="{{ routes('BE_DEALER_COLLECTION') }}" class="py-2 px-3 inline-flex justify-center items-center text-start bg-white border border-gray-200 text-gray-800 text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                    Annulla
                </a>

                <button type="submit" class="py-2 px-3 inline-flex justify-center items-center gap-x-2 text-start bg-blue-600 border border-blue-600 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-1 focus:ring-blue-300 dark:focus:ring-blue-500" {% if not user.hasPermission('DEALER_MANAGEMENT', 'create') and curDealer.id is empty %}disabled{% endif %} {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') and curDealer.id is not empty %}disabled{% endif %}>
                    {% if curDealer.id is not empty %}
                        Salva Modifiche
                    {% else %}
                        Aggiungi Rivenditore
                    {% endif %}
                </button>
            </div>
        </div>
        <!-- End Footer -->
    </form>
</div>
<!-- End Card -->