package utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.City;
import pojo.Country;
import pojo.Province;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Utility class for processing Excel files
 * 
 * <AUTHOR>
 */
public class ExcelUtils {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelUtils.class.getName());
    
    /**
     * Parse Excel file for Cities (Comuni)
     * Expected columns: denominazione_ita, codice_istat, codice_belfiore, cap, sigla_provincia, denominazione_provincia, denominazione_regione
     */
    public static List<City> parseCitiesExcel(byte[] fileContent) {
        List<City> cities = new ArrayList<>();

        try (ByteArrayInputStream bis = new ByteArrayInputStream(fileContent);
             Workbook workbook = new XSSFWorkbook(bis)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            // Skip informations
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }
            // Skip header row
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();

                try {
                    City city = new City();

                    // codice_istat
                    Cell codiceIstatCell = row.getCell(0);
                    if (codiceIstatCell != null) {
                        city.setCodiceIstat(getCellValueAsString(codiceIstatCell));
                    }

                    // denominazione_ita
                    Cell nameCell = row.getCell(2);
                    if (nameCell != null) {
                        city.setName(getCellValueAsString(nameCell));
                    }

                    // cap
                    Cell capCell = row.getCell(4);
                    if (capCell != null) {
                        city.setCap(getCellValueAsString(capCell));
                    }

                    // sigla_provincia
                    Cell provinceCodeCell = row.getCell(5);
                    if (provinceCodeCell != null) {
                        city.setProvinceCode(getCellValueAsString(provinceCodeCell));
                    }

                    // denominazione_provincia
                    Cell provinceCell = row.getCell(6);
                    if (provinceCell != null) {
                        city.setProvince(getCellValueAsString(provinceCell));
                    }

                    // denominazione_regione
                    Cell regionCell = row.getCell(9);
                    if (regionCell != null) {
                        city.setRegion(getCellValueAsString(regionCell));
                    }

                    // codice_belfiore
                    Cell codiceBelfioreCell = row.getCell(13);
                    if (codiceBelfioreCell != null) {
                        city.setCodiceBelfiore(getCellValueAsString(codiceBelfioreCell));
                    }

                    // Set fixed country code for Italy
                    city.setCountryCode("IT");

                    // Only add if we have at least name and codice_istat
                    if (city.getName() != null && !city.getName().trim().isEmpty() &&
                            city.getCodiceIstat() != null && !city.getCodiceIstat().trim().isEmpty()) {
                        cities.add(city);
                    }

                } catch (Exception ex) {
                    LOGGER.warn("Error processing city row " + row.getRowNum() + ": " + ex.getMessage());
                }
            }

        } catch (Throwable ex) {
            LOGGER.error("Error parsing cities Excel file", ex);
        }
        
        return cities;
    }
    
    /**
     * Parse Excel file for Countries (Stati)
     * Expected columns: sigla_nazione, codice_belfiore, denominazione_nazione
     */
    public static List<Country> parseCountriesExcel(byte[] fileContent) {
        List<Country> countries = new ArrayList<>();
        
        try (ByteArrayInputStream bis = new ByteArrayInputStream(fileContent);
             Workbook workbook = new XSSFWorkbook(bis)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            // Skip informations
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }
            // Skip header row
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }
            
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                
                try {
                    Country country = new Country();
                    
                    // sigla_nazione
                    Cell codeCell = row.getCell(0);
                    if (codeCell != null) {
                        country.setCode(getCellValueAsString(codeCell));
                    }
                    
                    // codice_belfiore
                    Cell codiceBelfioreCell = row.getCell(1);
                    if (codiceBelfioreCell != null) {
                        country.setCodiceBelfiore(getCellValueAsString(codiceBelfioreCell));
                    }
                    
                    // denominazione_nazione
                    Cell descriptionCell = row.getCell(2);
                    if (descriptionCell != null) {
                        country.setDescription(getCellValueAsString(descriptionCell));
                    }
                    
                    // Only add if we have at least code and description
                    if (country.getCode() != null && !country.getCode().trim().isEmpty() &&
                        country.getDescription() != null && !country.getDescription().trim().isEmpty()) {
                        countries.add(country);
                    }
                    
                } catch (Exception ex) {
                    LOGGER.warn("Error processing country row " + row.getRowNum() + ": " + ex.getMessage());
                }
            }
            
        } catch (Throwable ex) {
            LOGGER.error("Error parsing countries Excel file", ex);
        }
        
        return countries;
    }

    /**
     * Parse Excel file for Provinces (Province)
     * Expected columns: codice_regione, sigla_provincia, denominazione_provincia, tipologia_provincia, numero_comuni, superficie_kmq, codice_sovracomunale
     * We only need: sigla_provincia (column 1) -> code, denominazione_provincia (column 2) -> description
     */
    public static List<Province> parseProvincesExcel(byte[] fileContent) {
        List<Province> provinces = new ArrayList<>();

        try (ByteArrayInputStream bis = new ByteArrayInputStream(fileContent);
             Workbook workbook = new XSSFWorkbook(bis)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            // Skip informations
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }
            // Skip header row
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();

                try {
                    Province province = new Province();

                    // sigla_provincia (column 1)
                    Cell codeCell = row.getCell(1);
                    if (codeCell != null) {
                        province.setCode(getCellValueAsString(codeCell));
                    }

                    // denominazione_provincia (column 2)
                    Cell descriptionCell = row.getCell(2);
                    if (descriptionCell != null) {
                        province.setDescription(getCellValueAsString(descriptionCell));
                    }

                    // Only add if we have at least code and description
                    if (province.getCode() != null && !province.getCode().trim().isEmpty() &&
                        province.getDescription() != null && !province.getDescription().trim().isEmpty()) {
                        provinces.add(province);
                    }

                } catch (Exception ex) {
                    LOGGER.warn("Error processing province row " + row.getRowNum() + ": " + ex.getMessage());
                }
            }

        } catch (Throwable ex) {
            LOGGER.error("Error parsing provinces Excel file", ex);
        }

        return provinces;
    }

    /**
     * Get cell value as string, handling different cell types
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // Handle numeric values, convert to string without decimal if it's a whole number
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception ex) {
                    try {
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == Math.floor(numericValue)) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    } catch (Exception ex2) {
                        return null;
                    }
                }
            case BLANK:
            case _NONE:
            default:
                return null;
        }
    }
}
