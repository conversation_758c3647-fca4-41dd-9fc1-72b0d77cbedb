package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Dealer;
import pojo.RoutesPermission;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;
import pojo.QueryOptions;
import dao.DaoFiltersOperation;

/**
 *
 * <AUTHOR>
 */
public class DealerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DealerController.class.getName());

    public static TemplateViewRoute be_dealer_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.DEALER_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_DEALER_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_dealer_view = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.DEALER_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("dealerId"));
        if (oid != null) {
            Dealer loadedDealer = BaseDao.getDocumentById(oid, Dealer.class);
            attributes.put("curDealer", loadedDealer);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Dealer loadedDealer = BaseDao.getDocumentByParentId(parentId, Dealer.class);
                if (loadedDealer != null) {
                    attributes.put("curDealer", loadedDealer);
                }
            }
        }

        return Core.render(Pages.BE_DEALER_VIEW, attributes, request);
    };

    public static TemplateViewRoute be_dealer_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.DEALER_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("dealerId"));
        if (oid != null) {
            Dealer loadedDealer = BaseDao.getDocumentById(oid, Dealer.class);
            attributes.put("curDealer", loadedDealer);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Dealer loadedDealer = BaseDao.getDocumentByParentId(parentId, Dealer.class);
                if (loadedDealer != null) {
                    attributes.put("curDealer", loadedDealer);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_DEALER_FORM, attributes, request);
    };
    
    public static TemplateViewRoute be_dealer_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.DEALER_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("dealerId"));
        if (oid != null) {
            Dealer loadedDealer = BaseDao.getDocumentById(oid, Dealer.class);
            attributes.put("curDealer", loadedDealer);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Dealer loadedDealer = BaseDao.getDocumentByParentId(parentId, Dealer.class);
                if (loadedDealer != null) {
                    attributes.put("curDealer", loadedDealer);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_DEALER_EDIT, attributes, request);
    };

    public static Route be_dealer_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.DEALER_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Dealer> loadedDealers;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedDealers = BaseDao.getDocumentsByFilters(Dealer.class, queryOptions, loadArchived);
        } else {
            loadedDealers = BaseDao.getDocumentsByFilters(Dealer.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedDealers.isEmpty()) {
            for (Dealer tmpDealer : loadedDealers) {
                List<String> row = new ArrayList<>();
                row.add(""); // prima colonna vuota per checkbox

                // Nome con link
                String nameLink = "<a href='" + RoutesUtils.contextPath(request) + Routes.BE_DEALER_EDIT + "?dealerId=" + tmpDealer.getId() + "' class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' dealerId='" +
                    tmpDealer.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpDealer.getFullname(), "N.D.") + "</a>";
                row.add(nameLink);

                row.add(StringUtils.defaultIfBlank(tmpDealer.getDealerCode(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpDealer.getEmail(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpDealer.getPhoneNumber(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpDealer.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpDealer.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_dealer_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("dealerId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.DEALER_MANAGEMENT.getCode(), requiredPermission);

        Dealer newDealer;
        if (oid != null) {
            newDealer = BaseDao.getDocumentById(oid, Dealer.class);
            RequestUtils.mergeFromParams(params, newDealer);
        } else {
            newDealer = RequestUtils.createFromParams(params, Dealer.class);
        }

        if (newDealer != null) {
            // Validate dealer code uniqueness
            if (StringUtils.isNotBlank(newDealer.getDealerCode())) {
                Dealer existingDealer = BaseDao.getDocumentByField("dealerCode", newDealer.getDealerCode(), Dealer.class);
                if (existingDealer != null && !existingDealer.getId().equals(newDealer.getId())) {
                    // Dealer code already exists for a different dealer
                    response.status(400);
                    return "Codice dealer già esistente. Utilizzare un codice diverso.";
                }
            }

            // Uso needToUpdate per evitare di scrivere due righe di log nel database dei backup
            // PS: da usare solo se c'è la gestione dei file
            boolean needToUpdate = false;
            if (oid == null) {
                oid = BaseDao.insertDocument(newDealer);
                newDealer.setId(oid);

                BaseDao.insertLog(user, newDealer, LogType.INSERT);
            } else {
                needToUpdate = true;
                BaseDao.insertLog(user, newDealer, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newDealer, "imageId", false);
            } else {
                if (!params.containsKey("sameImage")) {
                    BaseDao.deleteImage(newDealer, "imageId");
                }
            }
            if (needToUpdate) {
                BaseDao.updateDocument(newDealer);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_dealer_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.DEALER_MANAGEMENT.getCode(), requiredPermission);

        String dealerIds = params.get("dealerIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(dealerIds)) {
            String[] ids = dealerIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Dealer tmpDealer = BaseDao.getDocumentById(oid, Dealer.class);
                    if (tmpDealer != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpDealer);
                                BaseDao.insertLog(user, tmpDealer, LogType.DELETE);
                                break;
                            case "archive":
                                tmpDealer.setArchived(true);
                                BaseDao.updateDocument(tmpDealer);
                                BaseDao.insertLog(user, tmpDealer, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpDealer.setArchived(false);
                                BaseDao.updateDocument(tmpDealer);
                                BaseDao.insertLog(user, tmpDealer, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
